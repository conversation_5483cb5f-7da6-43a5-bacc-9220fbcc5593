import { twMerge } from 'tailwind-merge'

const LogoIcon = ({
  className,
  size = 29,
}: {
  className?: string
  size?: number
}) => {
  return (
    <>
    
    <svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.9987 12.7585L16.2415 12.0013C16.8977 11.1432 17.453 10.3607 17.9073 9.65405C18.3616 8.9221 18.7528 8.22802 19.0809 7.5718C19.4343 6.91558 19.775 6.30983 20.1031 5.75457C20.4312 5.1993 20.7972 4.71975 21.201 4.31592C21.7815 3.73542 22.3999 3.44517 23.0561 3.44517C23.6619 3.44517 24.2298 3.71018 24.7598 4.24021C25.2898 4.77023 25.5548 5.33812 25.5548 5.94386C25.5548 6.60009 25.2646 7.21845 24.6841 7.79896C24.2802 8.20278 23.8007 8.56876 23.2454 8.89687C22.6902 9.22498 22.0844 9.56571 21.4282 9.91906C20.772 10.2472 20.0779 10.6384 19.346 11.0927C18.6393 11.547 17.8568 12.1023 16.9987 12.7585ZM5.94386 25.5548C5.33812 25.5548 4.77023 25.2898 4.24021 24.7598C3.71018 24.2298 3.44517 23.6619 3.44517 23.0561C3.44517 22.3999 3.73542 21.7815 4.31593 21.201C4.745 20.772 5.22454 20.3934 5.75457 20.0653C6.30983 19.7372 6.91558 19.4091 7.5718 19.0809C8.22802 18.7276 8.90949 18.3364 9.61619 17.9073C10.3481 17.453 11.1432 16.8977 12.0013 16.2415L12.7585 16.9987C12.1023 17.8568 11.547 18.6519 11.0927 19.3838C10.6636 20.0905 10.2724 20.772 9.91906 21.4282C9.59095 22.0844 9.26284 22.6902 8.93473 23.2454C8.60661 23.7755 8.22802 24.255 7.79895 24.6841C7.21845 25.2646 6.60009 25.5548 5.94386 25.5548ZM2.57441 16.9608C1.76675 16.9608 1.13577 16.7463 0.681462 16.3172C0.227154 15.8629 0 15.2572 0 14.5C0 13.7428 0.227154 13.1497 0.681462 12.7206C1.13577 12.2663 1.76675 12.0392 2.57441 12.0392C3.15492 12.0392 3.74804 12.1149 4.35379 12.2663C4.98477 12.4178 5.65361 12.607 6.36031 12.8342C7.09225 13.0614 7.86205 13.2759 8.66971 13.4778C9.47737 13.6797 10.4238 13.8438 11.5091 13.97V15.03C10.4238 15.1562 9.47737 15.3203 8.66971 15.5222C7.86205 15.7241 7.09225 15.9386 6.36031 16.1658C5.65361 16.3929 4.98477 16.5822 4.35379 16.7337C3.74804 16.8851 3.15492 16.9608 2.57441 16.9608ZM14.5 29C13.7428 29 13.1371 28.7728 12.6828 28.3185C12.2537 27.8642 12.0392 27.2332 12.0392 26.4256C12.0392 25.8451 12.1149 25.252 12.2663 24.6462C12.4178 24.0152 12.607 23.3464 12.8342 22.6397C13.0614 21.9077 13.2759 21.1379 13.4778 20.3303C13.6797 19.5226 13.8438 18.5762 13.97 17.4909H15.03C15.1562 18.5762 15.3203 19.5226 15.5222 20.3303C15.7241 21.1379 15.9386 21.9077 16.1658 22.6397C16.3929 23.3464 16.5822 24.0152 16.7337 24.6462C16.8851 25.252 16.9608 25.8451 16.9608 26.4256C16.9608 27.2332 16.7337 27.8642 16.2794 28.3185C15.8503 28.7728 15.2572 29 14.5 29ZM14.5379 18.248C13.503 18.248 12.607 17.8947 11.8499 17.188C11.1179 16.456 10.752 15.5727 10.752 14.5379C10.752 13.503 11.1179 12.6197 11.8499 11.8877C12.607 11.1305 13.503 10.752 14.5379 10.752C15.5727 10.752 16.4434 11.1305 17.1501 11.8877C17.8821 12.6197 18.248 13.503 18.248 14.5379C18.248 15.5727 17.8821 16.456 17.1501 17.188C16.4434 17.8947 15.5727 18.248 14.5379 18.248ZM12.0013 12.7585C11.1432 12.1023 10.3481 11.547 9.61619 11.0927C8.90949 10.6384 8.22802 10.2472 7.5718 9.91906C6.91558 9.56571 6.30983 9.22498 5.75457 8.89687C5.22454 8.56876 4.745 8.20278 4.31593 7.79896C3.73542 7.21845 3.44517 6.60009 3.44517 5.94386C3.44517 5.33812 3.71018 4.77023 4.24021 4.24021C4.77023 3.71018 5.33812 3.44517 5.94386 3.44517C6.60009 3.44517 7.21845 3.73542 7.79895 4.31592C8.22802 4.71975 8.60661 5.1993 8.93473 5.75457C9.26284 6.30983 9.59095 6.91558 9.91906 7.5718C10.2724 8.22802 10.6636 8.9221 11.0927 9.65405C11.547 10.3607 12.1023 11.1432 12.7585 12.0013L12.0013 12.7585ZM23.0561 25.5548C22.3999 25.5548 21.7815 25.2646 21.201 24.6841C20.7972 24.255 20.4312 23.7755 20.1031 23.2454C19.775 22.6902 19.4343 22.0844 19.0809 21.4282C18.7528 20.772 18.3616 20.0905 17.9073 19.3838C17.453 18.6519 16.8977 17.8568 16.2415 16.9987L16.9987 16.2415C17.8568 16.8977 18.6393 17.453 19.346 17.9073C20.0779 18.3364 20.772 18.7276 21.4282 19.0809C22.0844 19.4091 22.6902 19.7372 23.2454 20.0653C23.8007 20.3934 24.2802 20.772 24.6841 21.201C25.2646 21.7815 25.5548 22.3999 25.5548 23.0561C25.5548 23.6619 25.2898 24.2298 24.7598 24.7598C24.2298 25.2898 23.6619 25.5548 23.0561 25.5548ZM26.4256 16.9608C25.8451 16.9608 25.252 16.8851 24.6462 16.7337C24.0405 16.5822 23.3716 16.3929 22.6397 16.1658C21.933 15.9386 21.1632 15.7241 20.3303 15.5222C19.5226 15.3203 18.5762 15.1562 17.4909 15.03V13.97C18.5762 13.8438 19.5226 13.6797 20.3303 13.4778C21.1632 13.2759 21.933 13.0614 22.6397 12.8342C23.3716 12.607 24.0405 12.4178 24.6462 12.2663C25.252 12.1149 25.8451 12.0392 26.4256 12.0392C27.2332 12.0392 27.8642 12.2663 28.3185 12.7206C28.7728 13.1497 29 13.7428 29 14.5C29 15.2572 28.7728 15.8629 28.3185 16.3172C27.8642 16.7463 27.2332 16.9608 26.4256 16.9608ZM13.97 11.5091C13.8438 10.4238 13.6797 9.47737 13.4778 8.66971C13.2759 7.83681 13.0614 7.06701 12.8342 6.36031C12.607 5.62837 12.4178 4.95953 12.2663 4.35379C12.1149 3.74804 12.0392 3.15491 12.0392 2.57441C12.0392 1.76675 12.2537 1.13577 12.6828 0.681461C13.1371 0.227153 13.7428 0 14.5 0C15.2572 0 15.8503 0.227153 16.2794 0.681461C16.7337 1.13577 16.9608 1.76675 16.9608 2.57441C16.9608 3.15491 16.8851 3.74804 16.7337 4.35379C16.5822 4.95953 16.3929 5.62837 16.1658 6.36031C15.9386 7.06701 15.7241 7.83681 15.5222 8.66971C15.3203 9.47737 15.1562 10.4238 15.03 11.5091H13.97Z" fill="white"/>
</svg>
</>
    // <svg
    //   width={size}
    //   height={size}
    //   viewBox="0 0 29 29"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    //   className={twMerge(className ? className : '')}
    // >
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M9.07727 23.0572C13.8782 26.307 20.4046 25.0496 23.6545 20.2487C26.9043 15.4478 25.6469 8.92133 20.846 5.67149C16.0451 2.42165 9.51862 3.67905 6.26878 8.47998C3.01894 13.2809 4.27634 19.8073 9.07727 23.0572ZM10.4703 23.1428C14.862 25.3897 20.433 23.2807 22.9135 18.4322C25.394 13.5838 23.8447 7.83194 19.4531 5.58511C15.0614 3.33829 9.49042 5.4473 7.00991 10.2957C4.52939 15.1442 6.07867 20.896 10.4703 23.1428Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M11.7222 24.2898C15.6865 25.58 20.35 22.1715 22.1385 16.6765C23.927 11.1815 22.1632 5.68099 18.1989 4.39071C14.2346 3.10043 9.5711 6.509 7.78261 12.004C5.99412 17.4989 7.75793 22.9995 11.7222 24.2898ZM12.9347 23.872C16.2897 24.5876 19.9174 20.9108 21.0374 15.6596C22.1574 10.4084 20.3457 5.57134 16.9907 4.85575C13.6357 4.14016 10.008 7.817 8.88797 13.0682C7.76793 18.3194 9.57971 23.1564 12.9347 23.872Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M13.8537 24.7382C16.5062 25.0215 19.1534 20.5972 19.7664 14.8563C20.3794 9.1155 18.7261 4.23202 16.0736 3.94879C13.4211 3.66556 10.7739 8.08983 10.1609 13.8307C9.54788 19.5715 11.2012 24.455 13.8537 24.7382ZM15.0953 22.9906C17.015 22.9603 18.5101 19.0742 18.4349 14.3108C18.3596 9.54747 16.7424 5.71058 14.8228 5.7409C12.9032 5.77123 11.408 9.6573 11.4833 14.4207C11.5585 19.184 13.1757 23.0209 15.0953 22.9906Z"
    //     fill="currentColor"
    //   />
    // </svg>
  )
}

export default LogoIcon
