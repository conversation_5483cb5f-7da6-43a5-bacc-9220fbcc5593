import { twMerge } from 'tailwind-merge'

const LogoType = ({
  className,
  width,
  height,
}: {
  className?: string
  width?: number
  height?: number
}) => {
  return (
    <svg
    viewBox="0 0 80 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={twMerge(className ? className : '')}
    width={width}
    height={height}
  >
    <path
      d="M2 4L11 20L20 4"
      stroke="currentColor"
      strokeWidth="2.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 4L11 13L16 4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31.5 19.1539L35.2 4.18967H37.8L41.5 19.1539H39.4L38.6 15.8H34.4L33.6 19.1539H31.5ZM34.9 14.1H38.1L36.5 7.2L34.9 14.1Z"
      fill="currentColor"
    />
    <path
      d="M43.2 19.1539V4.18967H50.8V5.98537H45.3V10.5H50.2V12.3H45.3V17.3H51V19.1539H43.2Z"
      fill="currentColor"
    />
    <path
      d="M52.8 19.1539V4.18967H60.4V5.98537H54.9V10.5H59.8V12.3H54.9V17.3H60.6V19.1539H52.8Z"
      fill="currentColor"
    />
    <path
      d="M62.5 19.1539L66.9 11.7L62.9 4.18967H65.2L68.2 10.2L71.2 4.18967H73.5L69.5 11.7L73.9 19.1539H71.6L68.2 13L64.8 19.1539H62.5Z"
      fill="currentColor"
    />
  </svg>
    // <svg
    //   viewBox="0 0 80 24"
    //   fill="none"
    //   xmlns="http://www.w3.org/2000/svg"
    //   className={twMerge(className ? className : '')}
    //   width={width}
    //   height={height}
    // >
    //   <path
    //     d="M31.1672 19.1539V4.18967H37.1316C37.9154 4.18967 38.628 4.38919 39.2694 4.78824C39.9107 5.17303 40.4166 5.70746 40.7871 6.39154C41.1719 7.07561 41.3643 7.83808 41.3643 8.67893C41.3643 9.54825 41.1719 10.3322 40.7871 11.0304C40.4166 11.7288 39.9107 12.2846 39.2694 12.6979C38.628 13.1112 37.9154 13.3178 37.1316 13.3178H33.0271V19.1539H31.1672ZM33.0271 11.5007H37.1743C37.6019 11.5007 37.9866 11.3796 38.3287 11.1373C38.6708 10.8808 38.9415 10.5387 39.1411 10.1113C39.3406 9.68368 39.4404 9.20623 39.4404 8.67893C39.4404 8.16586 39.3406 7.70982 39.1411 7.31077C38.9415 6.91173 38.6708 6.59106 38.3287 6.34878C37.9866 6.1065 37.6019 5.98537 37.1743 5.98537H33.0271V11.5007Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     d="M48.2523 19.3676C47.155 19.3676 46.1716 19.1182 45.3023 18.6194C44.4471 18.1064 43.7703 17.4152 43.2714 16.5458C42.7726 15.6622 42.5232 14.6575 42.5232 13.5316C42.5232 12.4058 42.7726 11.4081 43.2714 10.5387C43.7703 9.66937 44.4471 8.98533 45.3023 8.48653C46.1716 7.98771 47.155 7.73831 48.2523 7.73831C49.3497 7.73831 50.3259 7.98771 51.1811 8.48653C52.0504 8.98533 52.7273 9.66937 53.2119 10.5387C53.7107 11.4081 53.9601 12.4058 53.9601 13.5316C53.9601 14.6575 53.7107 15.6622 53.2119 16.5458C52.7273 17.4152 52.0504 18.1064 51.1811 18.6194C50.3259 19.1182 49.3497 19.3676 48.2523 19.3676ZM48.2523 17.7002C49.0077 17.7002 49.6775 17.5221 50.2618 17.1658C50.8461 16.7952 51.3022 16.2964 51.63 15.6694C51.972 15.0423 52.1359 14.3297 52.1217 13.5316C52.1359 12.7335 51.972 12.0281 51.63 11.4153C51.3022 10.7882 50.8461 10.2965 50.2618 9.94015C49.6775 9.58391 49.0077 9.40576 48.2523 9.40576C47.497 9.40576 46.82 9.58391 46.2214 9.94015C45.6372 10.2965 45.1811 10.7882 44.8534 11.4153C44.5255 12.0423 44.3617 12.7478 44.3617 13.5316C44.3617 14.3297 44.5255 15.0423 44.8534 15.6694C45.1811 16.2964 45.6372 16.7952 46.2214 17.1658C46.82 17.5221 47.497 17.7002 48.2523 17.7002Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     d="M55.9135 19.1539V3.33456H57.7092V19.1539H55.9135Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     d="M64.7936 19.3676C63.8388 19.3676 62.9694 19.1182 62.1856 18.6194C61.416 18.1064 60.8032 17.408 60.3471 16.5244C59.8911 15.6409 59.6631 14.6432 59.6631 13.5316C59.6631 12.4058 59.8982 11.4081 60.3685 10.5387C60.8388 9.66937 61.4659 8.98533 62.2497 8.48653C63.0478 7.98771 63.9386 7.73831 64.9219 7.73831C65.5062 7.73831 66.0407 7.82383 66.5252 7.99484C67.024 8.16586 67.4658 8.40814 67.8507 8.72167C68.2354 9.02096 68.5561 9.37725 68.8126 9.7905C69.0691 10.1896 69.2401 10.6171 69.3257 11.0732L68.8554 10.8594L68.8767 7.97347H70.6724V19.1539H68.8767V16.439L69.3257 16.2038C69.2259 16.617 69.0335 17.0161 68.7485 17.401C68.4777 17.7857 68.1357 18.1277 67.7224 18.427C67.3233 18.7121 66.8744 18.9401 66.3756 19.1111C65.8767 19.2821 65.3495 19.3676 64.7936 19.3676ZM65.2212 17.6788C65.9481 17.6788 66.5894 17.5007 67.1452 17.1444C67.701 16.7881 68.1428 16.3035 68.4706 15.6907C68.7983 15.0636 68.9623 14.3439 68.9623 13.5316C68.9623 12.7335 68.7983 12.0281 68.4706 11.4153C68.157 10.8025 67.7152 10.3178 67.1452 9.96161C66.5894 9.60528 65.9481 9.42711 65.2212 9.42711C64.4943 9.42711 63.853 9.60528 63.2973 9.96161C62.7414 10.3178 62.2996 10.8025 61.9718 11.4153C61.6583 12.0281 61.5016 12.7335 61.5016 13.5316C61.5016 14.3297 61.6583 15.0423 61.9718 15.6694C62.2996 16.2964 62.7414 16.7881 63.2973 17.1444C63.853 17.5007 64.4943 17.6788 65.2212 17.6788Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     d="M72.9796 19.1539V7.97346H74.7753L74.818 11.116L74.6257 10.5815C74.7824 10.0542 75.0389 9.57676 75.3953 9.14922C75.7515 8.72167 76.1719 8.37964 76.6565 8.1231C77.1553 7.86658 77.6826 7.73831 78.2384 7.73831C78.4807 7.73831 78.7088 7.7597 78.9225 7.80244C79.1506 7.83095 79.3358 7.8737 79.4783 7.93071L78.9866 9.91878C78.8014 9.83332 78.609 9.76914 78.4095 9.72641C78.2099 9.68368 78.0246 9.66231 77.8536 9.66231C77.3976 9.66231 76.9772 9.74777 76.5924 9.91878C76.2218 10.0898 75.9012 10.325 75.6304 10.6243C75.3738 10.9093 75.1672 11.2442 75.0104 11.629C74.8679 12.0138 74.7967 12.4271 74.7967 12.8689V19.1539H72.9796Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M5.08734 21.1119C10.2671 24.6182 17.3085 23.2616 20.8148 18.0818C24.3211 12.9021 22.9645 5.86065 17.7848 2.35436C12.605 -1.15192 5.56353 0.204698 2.05724 5.38446C-1.44904 10.5642 -0.0924184 17.6057 5.08734 21.1119ZM6.58958 21.2045C11.3278 23.6286 17.3384 21.3531 20.0147 16.1221C22.6909 10.891 21.0194 4.68533 16.2811 2.2612C11.543 -0.162919 5.53235 2.11252 2.8561 7.34355C0.179842 12.5745 1.85138 18.7803 6.58958 21.2045Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M7.93988 22.4416C12.2169 23.8337 17.2485 20.1561 19.1782 14.2276C21.1078 8.29898 19.2047 2.3644 14.9276 0.9723C10.6505 -0.419794 5.61905 3.25775 3.68942 9.18633C1.7598 15.1149 3.6628 21.0495 7.93988 22.4416ZM9.24825 21.991C12.868 22.7631 16.7819 18.796 17.9904 13.1305C19.1988 7.46494 17.244 2.24622 13.6243 1.47416C10.0046 0.702105 6.09064 4.66908 4.88222 10.3347C3.67381 16.0002 5.62854 21.2189 9.24825 21.991Z"
    //     fill="currentColor"
    //   />
    //   <path
    //     fillRule="evenodd"
    //     clipRule="evenodd"
    //     d="M10.2406 22.9252C13.1024 23.2308 15.9585 18.4574 16.6199 12.2635C17.2812 6.06969 15.4974 0.800854 12.6356 0.495275C9.77386 0.189695 6.91772 4.96309 6.25634 11.157C5.59498 17.3508 7.37878 22.6196 10.2406 22.9252ZM11.5798 21.04C13.6508 21.0073 15.264 16.8146 15.1828 11.6754C15.1017 6.53608 13.3568 2.39642 11.2858 2.42914C9.21463 2.46187 7.60148 6.65457 7.68268 11.7939C7.76387 16.9331 9.50864 21.0727 11.5798 21.04Z"
    //     fill="currentColor"
    //   />
    // </svg>
  )
}

export default LogoType
