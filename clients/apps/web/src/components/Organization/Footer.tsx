import Link from 'next/link'
import { PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import { PolarLogotype } from '../Layout/Public/PolarLogotype'

const Footer = () => {
  return (
    <div className="mt-16 flex w-full flex-col items-center gap-y-12 bg-white dark:bg-black">
      <div
        className={twMerge(
          'flex w-full flex-col items-center px-6 py-16 md:max-w-3xl md:px-0 xl:max-w-6xl',
        )}
      >
        <div
          className={twMerge(
            'grid w-full grid-cols-1 gap-12 md:grid-cols-2 md:justify-between md:gap-24 lg:grid-cols-6',
          )}
        >
          <div className="flex flex-1 flex-col gap-y-6 md:col-span-2">
            <span className="text-black md:ml-0 dark:text-white">
              <PolarLogotype
                className="ml-2 md:ml-0"
                logoVariant="logotype"
                size={120}
              />
            </span>
            <span className="dark:text-polar-500 w-full grow text-gray-500">
              &copy; Pluggou Software LTDA. {new Date().getFullYear()}
            </span>
          </div>

          <div className="flex flex-col gap-y-4">
            <h3 className="text-base dark:text-white">Platform</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="https://status.polar.sh">Status</FooterLink>
              <FooterLink href="/login">Get Started</FooterLink>
              <FooterLink href="https://polar.sh/docs/documentation/features/products">
                Products & Subscriptions
              </FooterLink>
              <FooterLink href="https://polar.sh/docs/documentation/features/checkouts/checkout-links">
                Checkouts
              </FooterLink>
              <FooterLink href="https://polar.sh/docs/documentation/features/customer-portal">
                Customer Portal
              </FooterLink>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <h3 className="text-base dark:text-white">Company</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="https://polar.sh/assets/brand/polar_brand.zip">
                Brand Assets
              </FooterLink>
              <FooterLink href="https://polar.sh/legal/terms">
                Terms of Service
              </FooterLink>
              <FooterLink href="https://polar.sh/legal/privacy">
                Privacy Policy
              </FooterLink>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <h3 className="text-base dark:text-white">Community</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="https://discord.gg/Pnhfz3UThd">
                Join our Discord
              </FooterLink>
              <FooterLink href="https://github.com/polarsource">
                GitHub
              </FooterLink>
              <FooterLink href="https://x.com/polar_sh">X / Twitter</FooterLink>
            </div>
          </div>
          <div className="flex flex-col gap-y-4">
            <h3 className="text-base dark:text-white">Support</h3>
            <div className="flex flex-col gap-y-2">
              <FooterLink href="https://polar.sh/docs">Docs</FooterLink>
              <FooterLink href="mailto:<EMAIL>">Contact</FooterLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Footer

const FooterLinkClassnames =
  'dark:text-polar-500 dark:hover:text-polar-50 flex flex-row items-center gap-x-1 text-gray-500 transition-colors hover:text-gray-500'

const FooterLink = (props: PropsWithChildren<{ href: string }>) => {
  const isExternal = props.href.toString().startsWith('http')

  if (isExternal) {
    return (
      <a className={FooterLinkClassnames} {...props}>
        {props.children}
      </a>
    )
  }

  return (
    <Link className={FooterLinkClassnames} {...props}>
      {props.children}
    </Link>
  )
}
