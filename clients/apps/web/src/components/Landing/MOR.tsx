import ArrowOutward from '@mui/icons-material/ArrowOutward'
import Button from '@polar-sh/ui/components/atoms/Button'
import Link from 'next/link'
import { SplitPromo } from './molecules/SplitPromo'

export const MerchantOfRecord = () => {
  return (
    <SplitPromo
      title="Compliance e Impostos Automáticos"
      description="Foque em criar e vender. Cuidamos de toda a parte fiscal, impostos e compliance para você vender tranquilo no Brasil."
      bullets={[
        'Notas fiscais automáticas',
        'Retenção de impostos conforme lei',
        'Relatórios fiscais mensais',
      ]}
      image="/assets/landing/transactions.jpg"
      cta1={
        <Link href="/resources/merchant-of-record">
          <Button variant="secondary" className="rounded-full">
            Saiba mais
            <span className="ml-2">
              <ArrowOutward fontSize="inherit" />
            </span>
          </Button>
        </Link>
      }
    />
  )
}
