'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useState } from 'react'
import { twMerge } from 'tailwind-merge'

interface BenefitItemProps {
  index: number
  title: string
  description: string
  isOpen: boolean
  onClick: (index: number) => void
  image?: string
}

const benefitItemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 1 } },
}

const accordionVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      duration: 1,
    },
  },
}

const BenefitItem = ({
  index,
  title,
  description,
  isOpen,
  onClick,
}: BenefitItemProps) => {
  return (
    <motion.button
      className={twMerge(
        'flex w-full flex-col items-start gap-y-1 py-4 text-left',
        isOpen ? 'cursor-default' : '',
      )}
      onClick={() => !isOpen && onClick(index)}
      variants={benefitItemVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
    >
      <div className="flex w-full flex-row items-center justify-between text-lg">
        {title}
        <span className="text-2xl">{isOpen ? '-' : '+'}</span>
      </div>
      {isOpen && (
        <motion.p
          className="dark:text-polar-500 text-gray-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {description}
        </motion.p>
      )}
    </motion.button>
  )
}

interface AccordionProps {
  items: BenefitItemProps[]
  activeItem: number
  setActiveItem: (index: number) => void
}

const Accordion = ({ items, activeItem, setActiveItem }: AccordionProps) => {
  return (
    <motion.div
      className="dark:divide-polar-700 dark:border-polar-700 flex flex-col divide-y divide-gray-200 border-y border-gray-200"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={accordionVariants}
    >
      {items.map((item, index) => (
        <BenefitItem
          {...item}
          key={item.title}
          index={index}
          isOpen={activeItem === index}
          onClick={setActiveItem}
        />
      ))}
    </motion.div>
  )
}

export const Benefits = () => {
  const [activeItem, setActiveItem] = useState<number>(0)

  const items = [
    {
      title: 'Cursos e Treinamentos',
      description: 'Venda acesso a cursos, aulas gravadas e treinamentos com entrega automática via email ou plataforma própria',
      image: '/assets/landing/license.jpg',
    },
    {
      title: 'Ebooks e Materiais',
      description: 'Entregue automaticamente PDFs, planilhas, templates e outros materiais digitais após a compra',
      image: '/assets/landing/file.jpg',
    },
    {
      title: 'Comunidades Exclusivas',
      description: 'Gerencie acessos a grupos VIP no Telegram, Discord ou WhatsApp com automação total',
      image: '/assets/landing/github.jpg',
    },
    {
      title: 'Mentorias e Consultorias',
      description: 'Venda sessões individuais ou em grupo com agendamento automático e lembretes',
      image: '/assets/landing/discord.jpg',
    },
    {
      title: 'Assinaturas e Recorrência',
      description:
        'Crie planos mensais ou anuais com cobrança automática e gestão de cancelamentos',
      image: '/assets/landing/note.jpg',
    },
  ]

  return (
    <div
      className={`dark:bg-polar-900 flex w-full flex-col overflow-hidden rounded-2xl bg-white md:flex-row-reverse md:items-stretch md:rounded-4xl`}
    >
      <div className="flex flex-1 grow flex-col gap-y-10 p-8 md:p-16">
        <div className="flex flex-col gap-y-4">
          <h2 className="text-2xl leading-normal! md:text-3xl">
            Produtos Digitais que Vendem Sozinhos
          </h2>
          <p className="dark:text-polar-500 text-lg text-gray-500">
            Configure seus produtos uma vez e deixe a automação cuidar de tudo: vendas, entregas, acessos e relacionamento com clientes.
          </p>
        </div>
        <Accordion
          items={items.map((item, index) => ({
            ...item,
            index,
            isOpen: activeItem === index,
            onClick: setActiveItem,
          }))}
          activeItem={activeItem}
          setActiveItem={setActiveItem}
        />
      </div>
      <motion.div
        className="relative flex flex-1 grow flex-col p-8 md:p-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1 }}
      >
        <Image
          className="absolute inset-0 h-full w-full object-cover"
          src={items[activeItem].image}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1280px) 75vw, 640px"
          loading="lazy"
          alt={items[activeItem].title}
        />
      </motion.div>
    </div>
  )
}
