'use client'

import GetStartedButton from '@/components/Auth/GetStartedButton'
import AssessmentOutlined from '@mui/icons-material/AssessmentOutlined'
import CheckOutlined from '@mui/icons-material/CheckOutlined'
import SpaceDashboardOutlined from '@mui/icons-material/SpaceDashboardOutlined'
import TrendingUpOutlined from '@mui/icons-material/TrendingUpOutlined'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { Hero } from '../Hero/Hero'
import { Section } from '../Section'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 1 } },
}

export const AnalyticsPage = () => {
  return (
    <div className="flex flex-col">
      <Section className="flex flex-col gap-y-32 pt-0 md:pt-0">
        <Hero
          title="Saiba Exatamente Onde Está Cada Real"
          description="PIX tem 40% mais conversão que cartão no Brasil. Veja em tempo real qual método seus clientes preferem e acompanhe suas entregas automáticas."
        >
          <GetStartedButton size="lg" text="Começar" />
        </Hero>

        <motion.div
          className="dark:bg-polar-900 flex w-full flex-col overflow-hidden rounded-2xl bg-white md:flex-row-reverse md:items-stretch"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <div className="flex flex-1 grow flex-col gap-y-10 p-8 md:p-16">
            <div className="flex flex-col gap-y-4">
              <div className="flex items-center gap-x-3">
                <h2 className="text-2xl leading-normal! md:text-3xl">
                  Métricas de Receita que Importam
                </h2>
              </div>
              <p className="dark:text-polar-500 text-lg text-gray-500">
                Acompanhe as métricas que impulsionam seu negócio. Veja conversão por PIX, cartão e boleto, receita recorrente e taxa de entrega automática bem-sucedida.
              </p>
            </div>
            <motion.ul
              className="dark:divide-polar-700 dark:border-polar-700 flex flex-col divide-y divide-gray-200 border-y border-gray-200"
              variants={containerVariants}
            >
              {[
                'Receita Recorrente Mensal (MRR) e tendências de crescimento',
                'Conversão por método: PIX vs Cartão vs Boleto',
                'Taxa de entrega automática bem-sucedida (99.8%)',
                'Análise de coorte de receita e retenção',
              ].map((item, i) => (
                <motion.li
                  key={i}
                  className="flex items-start gap-x-3 py-2"
                  variants={itemVariants}
                >
                  <CheckOutlined
                    className="mt-0.5 text-emerald-500"
                    fontSize="small"
                  />
                  <span>{item}</span>
                </motion.li>
              ))}
            </motion.ul>
          </div>
          <div className="dark:bg-polar-800 relative flex flex-1 items-center justify-center p-8 md:p-16">
            <motion.div
              className="dark:bg-polar-900 dark:border-polar-700 z-10 flex w-full max-w-xs flex-col gap-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
              variants={itemVariants}
            >
              <div className="flex flex-row items-center justify-between gap-x-2">
                <span className="text-sm font-medium text-black dark:text-white">
                  Receita Recorrente Mensal
                </span>
                <TrendingUpOutlined
                  className="text-emerald-500"
                  fontSize="small"
                />
              </div>
              <div className="flex flex-col gap-y-1">
                <span className="text-3xl text-black dark:text-white">
                  R$ 48.392
                </span>
                <span className="dark:text-polar-500 text-sm text-gray-500">
                  <span className="text-emerald-500">+12.5%</span> do mês
                  passado
                </span>
              </div>
              <div className="dark:border-polar-700 flex items-center justify-between border-t border-gray-200 pt-4">
                <div className="flex flex-col">
                  <span className="dark:text-polar-500 text-xs text-gray-500">
                    PIX (45%)
                  </span>
                  <span className="font-medium text-black dark:text-white">
                    R$ 21.776
                  </span>
                </div>
                <div className="flex flex-col">
                  <span className="dark:text-polar-500 text-xs text-gray-500">
                    Cartão (35%)
                  </span>
                  <span className="font-medium text-black dark:text-white">
                    R$ 16.937
                  </span>
                </div>
              </div>
            </motion.div>
            <Image
              src="/assets/landing/abstract_08.jpg"
              alt="Analytics"
              className="absolute inset-0 h-full w-full object-cover"
              width={500}
              height={500}
            />
          </div>
        </motion.div>

        <Hero
          title="Insights de Clientes Brasileiros"
          description="Entenda seus clientes com segmentação detalhada, rastreamento de comportamento e análise de preferência de pagamento"
        >
          <div className="grid flex-1 grid-cols-1 gap-8 md:grid-cols-3">
            {[
              {
                icon: <TrendingUpOutlined fontSize="large" />,
                title: 'Métricas Detalhadas',
                description:
                  'Acompanhe receita, entenda comportamento do cliente e identifique oportunidades de crescimento',
              },
              {
                icon: <SpaceDashboardOutlined fontSize="large" />,
                title: 'Dashboard para Criadores',
                description:
                  'Tenha uma visão 360° do seu negócio com o Dashboard da Pluggou',
              },
              {
                icon: <AssessmentOutlined fontSize="large" />,
                title: 'Análise de Conversão',
                description:
                  'Analise qual método de pagamento converte mais: PIX, cartão ou boleto',
              },
            ].map((feature, i) => (
              <div
                key={i}
                className="dark:bg-polar-900 flex flex-col items-center gap-y-8 rounded-xl bg-white px-6 py-12 text-center"
              >
                <div className="flex flex-row gap-x-2">{feature.icon}</div>
                <div className="flex flex-col gap-y-4">
                  <h3 className="text-2xl">{feature.title}</h3>
                  <p className="dark:text-polar-400 text-balance text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Hero>
      </Section>

      <Section className="flex flex-col gap-y-24">
        <motion.div
          className="flex flex-col items-center gap-y-8 text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2 className="text-2xl md:text-3xl" variants={itemVariants}>
            Pronto para descobrir insights poderosos?
          </motion.h2>
          <motion.p
            className="dark:text-polar-500 text-lg text-gray-500 md:w-[480px]"
            variants={itemVariants}
          >
            Junte-se a criadores usando analytics da Pluggou para impulsionar crescimento e tomar decisões baseadas em dados.
          </motion.p>
          <motion.div variants={itemVariants}>
            <GetStartedButton size="lg" text="Começar Agora" />
          </motion.div>
        </motion.div>
      </Section>
    </div>
  )
}
