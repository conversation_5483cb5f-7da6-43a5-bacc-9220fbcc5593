import { ProductsPage } from '@/components/Landing/features/ProductsPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Produtos Digitais com Entrega Automática — Pluggou',
  description:
    'Cobrança flexível com múltiplos modelos de preços, períodos de teste e gestão perfeita de planos. Entrega automática via PIX, cartão ou boleto.',
  keywords:
    'produtos digitais, assinaturas, pagamentos recorrentes, pix, cartão, boleto, entrega automática',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <ProductsPage />
}
