import { BenefitsPage } from '@/components/Landing/features/BenefitsPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Entregas Automáticas — Pluggou',
  description:
    'A única plataforma que entrega automaticamente seus produtos após cada venda via PIX, cartão ou boleto.',
  keywords:
    'entrega automática, produtos digitais, pix, cartão, boleto, automação, benefícios, chaves de licença',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <BenefitsPage />
}
