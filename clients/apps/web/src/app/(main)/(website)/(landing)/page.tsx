import { Metadata } from 'next'
import LandingPage from '../../../../components/Landing/LandingPage'

export const metadata: Metadata = {
  title: 'Pluggou — A plataforma completa para monetizar sua audiência',
  description: 'Transforme sua paixão em renda. Venda cursos, ebooks, acessos VIP e comunidades com PIX, automação total e checkout brasileiro otimizado.',
  keywords:
    'monetização digital, criadores de conteúdo, infoprodutos, PIX, comunidades VIP, cursos online, ebooks, influenciadores, renda extra, economia criativa',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <LandingPage />
}
