import { UsageBillingPage } from '@/components/Landing/features/UsageBillingPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Cobrança Automática que Não Para — Pluggou',
  description:
    'Assinaturas mensais ou anuais com cobrança automática via cartão ou PIX. Cliente cancela quando quiser. Entrega automática a cada renovação.',
  keywords:
    'cobrança recorrente, assinaturas, pix, cartão, automático, renovação, entrega automática',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <UsageBillingPage />
}
