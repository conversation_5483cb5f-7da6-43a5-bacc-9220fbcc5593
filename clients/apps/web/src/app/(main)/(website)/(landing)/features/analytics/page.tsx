import { AnalyticsPage } from '@/components/Landing/features/AnalyticsPage'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Analytics e Métricas — Pluggou',
  description:
    'Acompanhe sua receita e crescimento. Veja em tempo real qual método de pagamento seus clientes preferem: PIX, cartão ou boleto.',
  keywords:
    'analytics, métricas, receita, pix, cartão, boleto, conversão, dashboard, crescimento',
  openGraph: {
    siteName: 'Pluggou',
    type: 'website',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: 'https://polar.sh/assets/brand/polar_og.jpg',
        width: 1200,
        height: 630,
        alt: 'Pluggou',
      },
    ],
  },
}

export default function Page() {
  return <AnalyticsPage />
}
