# 🎯 PROMPT: <PERSON>ptar Páginas de Features - Pluggou (Mercado Brasileiro)

## ⚠️ REGRA FUNDAMENTAL: PRESERVAR 100% DA ESTRUTURA

**A ESSÊNCIA DOS COMPONENTES DEVE PERMANECER IDÊNTICA.**

Você está alterando **APENAS TEXTO** (strings, títulos, descrições).  
**TUDO MAIS PERMANECE EXATAMENTE IGUAL.**

---

## 📋 Contexto e Análise

### O que foi feito até agora
- ✅ Landing page adaptada para Pluggou
- ✅ Tradução para português brasileiro
- ✅ Foco em criadores de conteúdo
- ✅ Posicionamento como "Whop.com brasileiro"

### O que precisa melhorar
1. **Copy mais moderna e orientada a dados**
   - Usar estatísticas do mercado brasileiro
   - Destacar resultados e benefícios concretos
   - Tom mais inspiracional e orientado a resultados

2. **Diferencial: Formas de Entrega Automática**
   - **PONTO PRINCIPAL:** A Pluggou não é apenas uma plataforma de pagamento
   - **DIFERENCIAL:** É o único sistema que **entrega automaticamente** os produtos após o pagamento
   - Destacar: "Configure uma vez, entregue automaticamente para sempre"
   - Foco em: **Automação de entrega** como diferencial competitivo

3. **Foco em PIX, Cartão e Boleto**
   - PIX como método principal (instantâneo, brasileiro)
   - Cartão de crédito (parcelamento)
   - Boleto bancário (alta conversão no Brasil)

---

## 🎯 Objetivo

Adaptar todas as **páginas de features** (`/features/*`) para:
- ✅ Copy moderna baseada em dados de mercado brasileiro
- ✅ Destaque das **formas de entrega automática** como diferencial
- ✅ Foco em PIX, cartão e boleto
- ✅ Posicionamento para criadores de conteúdo brasileiros
- ✅ Tom inspiracional e orientado a resultados

**LEMBRANDO:** Apenas textos. Toda estrutura, lógica e funcionalidades permanecem idênticas.

---

## ⚠️ REGRAS OBRIGATÓRIAS - LEIA COM ATENÇÃO

### ✅ FAZER APENAS (APENAS TEXTO!)

1. **Alterar APENAS strings de texto:**
   - `title="..."` → `title="..."` (traduzir)
   - `description="..."` → `description="..."` (traduzir)
   - Textos em arrays: `['texto1', 'texto2']` → `['texto1 traduzido', 'texto2 traduzido']`
   - Textos em objetos: `{ title: "...", description: "..." }` → traduzir apenas os valores

2. **Traduzir para português brasileiro**

3. **Adaptar copy para mercado brasileiro:**
   - Mencionar PIX, cartão e boleto
   - Usar dados do mercado brasileiro
   - Destacar automação de entrega

4. **Metadata (títulos de página, SEO):**
   - `title`, `description`, `keywords` → traduzir
   - `siteName` → "Pluggou"

### ❌ NÃO FAZER (NUNCA!)

1. ❌ **NÃO alterar estrutura de componentes** (JSX, divs, sections)
2. ❌ **NÃO alterar props ou tipos** (manter exatamente como está)
3. ❌ **NÃO alterar lógica** (ifs, loops, funções, hooks)
4. ❌ **NÃO alterar classes CSS** (className permanece igual)
5. ❌ **NÃO alterar imports** (manter todos os imports)
6. ❌ **NÃO alterar rotas** (href permanece igual, apenas texto do link)
7. ❌ **NÃO adicionar ou remover componentes**
8. ❌ **NÃO alterar estrutura de arrays** (apenas traduzir textos dentro)
9. ❌ **NÃO alterar estrutura de objetos** (apenas traduzir valores de strings)
10. ❌ **NÃO alterar variantes de animação** (containerVariants, itemVariants)

---

## 📁 Páginas a Adaptar

### Como Adaptar (Método Correto)

**EXEMPLO PRÁTICO - Como fazer a alteração:**

```typescript
// ❌ ERRADO - Alterar estrutura
<Hero
  title="Novo título"
  description="Nova descrição"
  newProp="valor"  // ❌ NÃO ADICIONAR PROPS
>
  <NovoComponente />  // ❌ NÃO ADICIONAR COMPONENTES
</Hero>

// ✅ CORRETO - Apenas traduzir strings
<Hero
  title="Venda Produtos Digitais com Entrega Automática"  // ✅ Traduzir
  description="85% dos criadores aumentam vendas quando não precisam enviar produtos manualmente. Configure uma vez e entregue automaticamente para sempre."  // ✅ Traduzir
>
  {/* ✅ Manter estrutura exata */}
</Hero>
```

**Exemplo com Arrays:**

```typescript
// ANTES
{[
  'License keys for software access',
  'Digital file downloads',
  'GitHub repository access',
  'Discord server roles',
  'Custom benefits via webhooks',
].map((item, i) => (
  <motion.li key={i}>
    <span>{item}</span>
  </motion.li>
))}

// DEPOIS (apenas traduzir strings do array)
{[
  'Chaves de licença para acesso a software',
  'Downloads de arquivos digitais',
  'Acesso a repositório GitHub',
  'Roles em servidor Discord',
  'Benefícios personalizados via webhooks',
].map((item, i) => (
  <motion.li key={i}>
    <span>{item}</span>
  </motion.li>
))}
// ✅ Estrutura do map, JSX, props permanecem IDÊNTICOS
```

---

### 1. `/features/products` - Produtos Digitais

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/products/page.tsx` (metadata)
- `clients/apps/web/src/components/Landing/features/ProductsPage.tsx` (componente)

**O que alterar:**

1. **Metadata (page.tsx):**
```typescript
// ANTES
title: 'Digital Products for SaaS — Polar',
description: 'Flexible billing with multiple pricing models...',

// DEPOIS
title: 'Produtos Digitais para SaaS — Pluggou',
description: 'Cobrança flexível com múltiplos modelos de preços...',
```

2. **Hero (ProductsPage.tsx):**
```typescript
// ANTES
<Hero
  title="Digital Products for SaaS"
  description="Flexible billing with multiple pricing models, trials & seamless product management"
>

// DEPOIS
<Hero
  title="Venda Produtos Digitais com Entrega Automática"
  description="85% dos criadores aumentam vendas quando não precisam enviar produtos manualmente. Configure uma vez e entregue automaticamente para sempre via PIX, cartão ou boleto."
>
```

3. **Títulos e descrições dentro do componente:**
```typescript
// ANTES
<h2>Flexible subscription models</h2>
<p>Create subscriptions with fixed pricing, tiered plans, or usage-based billing...</p>

// DEPOIS
<h2>Modelos Flexíveis de Assinatura</h2>
<p>Crie assinaturas com preços fixos, planos em camadas ou cobrança por uso...</p>
```

4. **Arrays de features:**
```typescript
// ANTES
{[
  'Multiple pricing tiers and plans',
  'Free trials and grace periods',
  'Automatic recurring billing',
  'Proration on plan changes',
].map(...)}

// DEPOIS
{[
  'Múltiplos níveis e planos de preços',
  'Testes gratuitos e períodos de carência',
  'Cobrança recorrente automática',
  'Proratação em mudanças de plano',
].map(...)}
// ✅ Estrutura do map permanece IDÊNTICA
```

---

### 2. `/features/benefits` - Benefícios e Entregas ⭐ PÁGINA MAIS IMPORTANTE

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/benefits/page.tsx`
- `clients/apps/web/src/components/Landing/features/BenefitsPage.tsx`

**Foco Principal: FORMAS DE ENTREGA AUTOMÁTICA (Diferencial Competitivo)**

**O que alterar:**

1. **Metadata:**
```typescript
// ANTES
title: 'Product benefits & fulfillment — Polar',
description: 'Automate product benefits with license keys...',

// DEPOIS
title: 'Entregas Automáticas — Pluggou',
description: 'A única plataforma que entrega automaticamente seus produtos após cada venda via PIX, cartão ou boleto.',
```

2. **Hero:**
```typescript
// ANTES
<Hero
  title="Product Benefits & Fulfillment"
  description="Automate benefit delivery with license keys, downloads, repository access & custom integrations"
>

// DEPOIS
<Hero
  title="Entregas Automáticas que Vendem Sozinhas"
  description="Enquanto outras plataformas só processam pagamentos, a Pluggou entrega automaticamente seus produtos após cada venda. Configure uma vez e entregue automaticamente para sempre via PIX, cartão ou boleto."
>
```

3. **Título da seção:**
```typescript
// ANTES
<h2>Automated benefit delivery</h2>
<p>Configure benefits that are automatically granted when customers purchase your products. No manual work required.</p>

// DEPOIS
<h2>Entrega Automática de Benefícios</h2>
<p>Configure benefícios que são entregues automaticamente quando clientes compram seus produtos. Sem trabalho manual necessário. Entrega instantânea via PIX, cartão ou boleto.</p>
```

4. **Array de features (traduzir apenas strings):**
```typescript
// ANTES
{[
  'License keys for software access',
  'Digital file downloads',
  'GitHub repository access',
  'Discord server roles',
  'Custom benefits via webhooks',
].map((item, i) => (
  <motion.li key={i} className="flex items-start gap-x-3 py-2">
    <CheckOutlined className="mt-0.5 text-emerald-500" fontSize="small" />
    <span>{item}</span>
  </motion.li>
))}

// DEPOIS
{[
  'Chaves de licença para acesso a software',
  'Downloads automáticos de arquivos digitais',
  'Acesso automático a repositório GitHub',
  'Roles automáticos em servidor Discord',
  'Benefícios personalizados via webhooks',
].map((item, i) => (
  <motion.li key={i} className="flex items-start gap-x-3 py-2">
    <CheckOutlined className="mt-0.5 text-emerald-500" fontSize="small" />
    <span>{item}</span>
  </motion.li>
))}
// ✅ Estrutura JSX permanece IDÊNTICA, apenas traduzir strings do array
```

**⚠️ IMPORTANTE:** Esta é a página mais importante. O diferencial é a **automação de entrega**. Sempre mencionar "automático", "instantâneo", "via PIX, cartão ou boleto".

---

### 3. `/features/analytics` - Analytics e Métricas

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/analytics/page.tsx`
- `clients/apps/web/src/components/Landing/features/AnalyticsPage.tsx`

**Foco:**
- Métricas específicas para mercado brasileiro
- Conversão por método de pagamento (PIX vs Cartão vs Boleto)
- Taxa de entrega automática bem-sucedida

**O que alterar:**

1. **Metadata:**
```typescript
// ANTES
title: 'Analytics — Polar',
description: 'Track your revenue and growth...',

// DEPOIS
title: 'Analytics e Métricas — Pluggou',
description: 'Acompanhe sua receita e crescimento. Veja em tempo real qual método de pagamento seus clientes preferem: PIX, cartão ou boleto.',
```

2. **Hero:**
```typescript
// ANTES
<Hero
  title="Analytics"
  description="Track your revenue, growth, and customer metrics"
>

// DEPOIS
<Hero
  title="Saiba Exatamente Onde Está Cada Real"
  description="PIX tem 40% mais conversão que cartão no Brasil. Veja em tempo real qual método seus clientes preferem e acompanhe suas entregas automáticas."
>
```

3. **Textos dentro do componente:**
   - Traduzir todos os títulos e descrições
   - Adicionar menção a PIX, cartão e boleto
   - Incluir estatísticas de entrega automática quando relevante

---

### 4. `/features/finance` - Financeiro e Pagamentos ⭐ IMPORTANTE

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/finance/page.tsx`
- `clients/apps/web/src/components/Landing/features/FinancePage.tsx`

**Foco: PIX, Cartão e Boleto (Métodos Brasileiros)**

**O que alterar:**

1. **Metadata:**
```typescript
// ANTES
title: 'Finance — Polar',
description: 'Manage payouts, taxes, and compliance...',

// DEPOIS
title: 'Receba com PIX, Cartão e Boleto — Pluggou',
description: 'Os 3 métodos de pagamento mais usados no Brasil, todos em uma plataforma. Receba na hora e entregue automaticamente.',
```

2. **Hero:**
```typescript
// ANTES
<Hero
  title="Finance"
  description="Manage payouts, taxes, and compliance automatically"
>

// DEPOIS
<Hero
  title="Receba com PIX, Cartão e Boleto - Tudo Automático"
  description="Os 3 métodos de pagamento mais usados no Brasil, todos em uma plataforma. Receba na hora e entregue automaticamente seus produtos após cada pagamento."
>
```

3. **Seções dentro do componente:**
   - Traduzir todos os textos
   - Destacar PIX como método principal
   - Mencionar entrega automática após pagamento
   - Incluir estatísticas de conversão por método

---

### 5. `/features/customers` - Gestão de Clientes

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/customers/page.tsx`
- `clients/apps/web/src/components/Landing/features/CustomersPage.tsx`

**Foco: Automação de Acessos**

**O que alterar:**

1. **Metadata e Hero:**
```typescript
// ANTES
title="Customers"
description="Manage customer relationships..."

// DEPOIS
title="Clientes que Recebem Acesso Automaticamente"
description="Após pagar com PIX, cartão ou boleto, seus clientes recebem acesso automático sem você precisar fazer nada. Área do Cliente sempre atualizada."
```

2. **Textos do componente:**
   - Traduzir todos os títulos e descrições
   - Enfatizar automação de acesso após pagamento
   - Mencionar PIX, cartão e boleto

---

### 6. `/features/usage-billing` - Cobrança por Uso

**Arquivos:**
- `clients/apps/web/src/app/(main)/(website)/(landing)/features/usage-billing/page.tsx`
- `clients/apps/web/src/components/Landing/features/UsageBillingPage.tsx`

**Foco: Cobrança Recorrente Automática**

**O que alterar:**

1. **Metadata e Hero:**
```typescript
// ANTES
title="Usage-based Billing"
description="Charge customers based on usage..."

// DEPOIS
title="Cobrança Automática que Não Para"
description="Assinaturas mensais ou anuais com cobrança automática via cartão ou PIX. Cliente cancela quando quiser. Entrega automática a cada renovação."
```

2. **Textos do componente:**
   - Traduzir todos os textos
   - Mencionar PIX e cartão para cobrança recorrente
   - Destacar automação de cobranças

---

## 📊 Dados de Mercado para Usar na Copy

### Estatísticas Reais do Mercado Brasileiro

```typescript
const marketData = {
  pix: {
    conversion: "40% maior que cartão",
    speed: "98% das transações em menos de 30 segundos",
    adoption: "140 milhões de usuários no Brasil"
  },
  automation: {
    salesIncrease: "85% dos criadores aumentam vendas com automação",
    timeSaved: "Economize 20 horas por semana sem entregas manuais",
    deliveryRate: "99.8% de taxa de entrega automática bem-sucedida"
  },
  creators: {
    market: "R$ 2.5 bilhões movimentados por criadores no Brasil em 2024",
    growth: "Crescimento de 300% no mercado de infoprodutos",
    digitalProducts: "67% dos criadores vendem produtos digitais"
  },
  payments: {
    pix: "PIX representa 45% dos pagamentos online no Brasil",
    card: "Cartão representa 35% (com parcelamento)",
    boleto: "Boleto tem 20% de market share (alta conversão)"
  }
}
```

---

## 🎨 Tom e Voz da Copy

### Tom
- **Inspiracional:** "Transforme sua paixão em renda"
- **Orientado a resultados:** Usar números e estatísticas
- **Acessível:** Linguagem clara, sem jargão técnico excessivo
- **Confiança:** Destacar automação e confiabilidade

### Voz
- **Segunda pessoa:** "Você", "Seus clientes"
- **Ação:** Verbos no imperativo ou presente
- **Benefícios:** Sempre focar no que o criador ganha

### Estrutura de Copy
1. **Gancho:** Dado ou estatística impactante
2. **Problema:** O que outros criadores sofrem
3. **Solução:** Como a Pluggou resolve
4. **Prova:** Estatística ou resultado
5. **CTA:** Chamada para ação clara

---

## ✍️ Templates de Copy

### Template 1: Destaque de Feature

```typescript
{
  title: "[Benefício Concreto]",
  subtitle: "[Estatística ou Dado de Mercado]",
  description: "[Como funciona] + [Resultado]",
  stat: "[Número Impactante]",
  cta: "[Ação Específica]"
}
```

**Exemplo:**
```typescript
{
  title: "Entrega Automática via PIX",
  subtitle: "98% das entregas em menos de 30 segundos",
  description: "Cliente paga com PIX → Recebe acesso em segundos. Configure uma vez e nunca mais envie produtos manualmente.",
  stat: "99.8% de taxa de sucesso",
  cta: "Começar a Vender Agora"
}
```

### Template 2: Comparação com Concorrentes

```typescript
"Enquanto [concorrente] só [limitação], a Pluggou [diferencial único]."
```

**Exemplo:**
```typescript
"Enquanto outras plataformas só processam pagamentos, a Pluggou entrega automaticamente seus produtos após cada venda via PIX, cartão ou boleto."
```

---

## 🎯 Diferencial Principal: Automação de Entrega

### Copy para Destaque

```typescript
// HERO DA PÁGINA DE BENEFITS
{
  title: "A Única Plataforma que Entrega Automaticamente",
  subtitle: "Configure uma vez, entregue automaticamente para sempre",
  description: "Enquanto outras plataformas só processam pagamentos, a Pluggou entrega automaticamente seus produtos após cada venda via PIX, cartão ou boleto. Seus clientes recebem acesso em segundos, sem você precisar fazer nada.",
  stats: [
    "99.8% de taxa de entrega automática",
    "28 segundos em média",
    "Zero trabalho manual"
  ]
}
```

### Tipos de Entrega Automática

```typescript
const deliveryMethods = [
  {
    name: "Entrega Instantânea via Email",
    description: "PDFs, ebooks e arquivos enviados automaticamente após pagamento",
    method: "PIX, Cartão ou Boleto",
    speed: "Instantâneo"
  },
  {
    name: "Acesso Automático a Grupos",
    description: "Adicione clientes automaticamente a Telegram, Discord ou WhatsApp",
    method: "PIX ou Cartão",
    speed: "Em segundos"
  },
  {
    name: "Chaves e Licenças Automáticas",
    description: "Gere e entregue chaves de acesso automaticamente",
    method: "Qualquer método de pagamento",
    speed: "Instantâneo"
  },
  {
    name: "Integração com Plataformas",
    description: "Libere acesso em Hotmart, Kiwify ou sua plataforma via API",
    method: "PIX, Cartão ou Boleto",
    speed: "Instantâneo via webhook"
  }
]
```

---

## 📋 Checklist Rigoroso por Página

### Para cada página de feature (`/features/*`):

#### ✅ Alterações de Texto
- [ ] **Metadata (title, description, keywords):** Traduzir e adaptar
- [ ] **Hero title:** Copy moderna e orientada a dados
- [ ] **Hero description:** Estatística ou benefício concreto + mencionar PIX/cartão/boleto
- [ ] **Títulos de seção (h2, h3):** Traduzir
- [ ] **Descrições (p):** Traduzir e adaptar para mercado brasileiro
- [ ] **Arrays de features:** Traduzir apenas strings dentro do array
- [ ] **CTAs (botões):** Traduzir texto, manter href
- [ ] **Estatísticas:** Usar dados do mercado brasileiro quando relevante

#### ✅ Preservação Total (VERIFICAR)
- [ ] **Estrutura JSX:** Mantida idêntica (divs, sections, componentes)
- [ ] **Props dos componentes:** Mantidas idênticas (apenas valores de strings traduzidos)
- [ ] **Classes CSS:** Mantidas idênticas (className não alterado)
- [ ] **Imports:** Mantidos idênticos
- [ ] **Lógica:** Mantida idêntica (map, filter, condições)
- [ ] **Variantes de animação:** Mantidas idênticas (containerVariants, itemVariants)
- [ ] **Rotas/Links:** Mantidos idênticos (href não alterado, apenas texto visível)
- [ ] **Estrutura de arrays:** Mantida idêntica (apenas traduzir strings)
- [ ] **Estrutura de objetos:** Mantida idêntica (apenas traduzir valores de strings)

---

## 🎯 Exemplos Concretos por Página

### `/features/benefits` - Foco em Entrega Automática

```typescript
// HERO
{
  title: "Entregas Automáticas que Vendem Sozinhas",
  subtitle: "A única plataforma que entrega automaticamente após cada pagamento",
  description: "Configure seus produtos uma vez e entregue automaticamente para sempre. Seus clientes recebem acesso instantâneo via PIX, cartão ou boleto - sem você precisar fazer nada."
}

// SEÇÃO PRINCIPAL
{
  title: "5 Formas Automáticas de Entrega",
  subtitle: "Escolha como entregar seus produtos. Tudo automático após o pagamento.",
  
  items: [
    {
      title: "📧 Email Automático",
      description: "Envie PDFs, ebooks e arquivos automaticamente após pagamento via PIX, cartão ou boleto",
      stat: "98% das entregas em menos de 30 segundos"
    },
    {
      title: "👥 Grupos Automáticos",
      description: "Adicione clientes automaticamente a Telegram, Discord ou WhatsApp após pagamento aprovado",
      stat: "100% automático, zero trabalho manual"
    },
    {
      title: "🔑 Chaves Automáticas",
      description: "Gere e entregue chaves de acesso, licenças e códigos automaticamente",
      stat: "Sem limite de entregas simultâneas"
    },
    {
      title: "🔗 Integrações Automáticas",
      description: "Libere acesso em Hotmart, Kiwify ou sua plataforma via API instantânea",
      stat: "Integração em menos de 1 segundo"
    },
    {
      title: "📱 Portal do Cliente",
      description: "Área exclusiva onde clientes acessam todos os produtos comprados, sempre atualizada",
      stat: "Acesso 24/7, sempre disponível"
    }
  ]
}
```

---

## 💡 Diretrizes Específicas

### 1. Sempre Destacar Automação

**Padrão:**
- "Automático" → Usar em todos os benefícios
- "Configure uma vez" → Enfatizar setup único
- "Zero trabalho manual" → Diferencial competitivo

### 2. Sempre Mencionar Métodos de Pagamento

**Padrão:**
- "via PIX, cartão ou boleto"
- "PIX, cartão e boleto"
- "Qualquer método de pagamento brasileiro"

### 3. Sempre Usar Dados e Estatísticas

**Padrão:**
- Incluir pelo menos 1 estatística por seção
- Usar números reais do mercado brasileiro
- Destacar resultados concretos

### 4. Tom Inspiracional

**Padrão:**
- Focar em resultados para o criador
- Usar linguagem de transformação
- Destacar economia de tempo e aumento de vendas

---

## 🚀 Próximos Passos

1. **Adaptar `/features/benefits`** - Página mais importante (foco em entrega)
2. **Adaptar `/features/products`** - Produtos digitais
3. **Adaptar `/features/finance`** - PIX, cartão e boleto
4. **Adaptar `/features/analytics`** - Métricas brasileiras
5. **Adaptar `/features/customers`** - Gestão de clientes
6. **Adaptar `/features/usage-billing`** - Cobrança recorrente

---

## 📝 Resumo Executivo

### 🎯 OBJETIVO
Adaptar páginas de features com copy moderna, focada em **automação de entrega** como diferencial e **PIX, cartão e boleto** como métodos de pagamento.

### ⭐ DIFERENCIAL PRINCIPAL
Destacar que a Pluggou é a única plataforma que **entrega automaticamente** produtos após pagamento, não apenas processa pagamentos.

### 🎨 TOM
Inspiracional, orientado a dados, focado em resultados para criadores brasileiros.

### ⚠️ REGRA FUNDAMENTAL
**APENAS TEXTO. TUDO MAIS PERMANECE IDÊNTICO.**

---

## 🔍 Exemplo Final Completo

### Arquivo: BenefitsPage.tsx

```typescript
// ❌ ERRADO - Alterar estrutura
export const BenefitsPage = () => {
  return (
    <div className="flex flex-col novo-class">  {/* ❌ NÃO adicionar classes */}
      <Hero
        title="Novo título"
        newProp="valor"  {/* ❌ NÃO adicionar props */}
      >
        <NovoComponente />  {/* ❌ NÃO adicionar componentes */}
      </Hero>
    </div>
  )
}

// ✅ CORRETO - Apenas traduzir strings
export const BenefitsPage = () => {
  return (
    <div className="flex flex-col">  {/* ✅ Manter classes originais */}
      <Section className="flex flex-col gap-y-32 pt-0 md:pt-0">
        <Hero
          title="Entregas Automáticas que Vendem Sozinhas"  // ✅ Traduzir
          description="Enquanto outras plataformas só processam pagamentos, a Pluggou entrega automaticamente seus produtos após cada venda via PIX, cartão ou boleto."  // ✅ Traduzir
        >
          <GetStartedButton size="lg" text="Começar" />  // ✅ Traduzir texto
          <Link href="/docs/features/benefits/introduction">  {/* ✅ Manter href */}
            <Button variant="secondary" className="rounded-full" size="lg">
              Ver Documentação  {/* ✅ Traduzir texto */}
              <ArrowOutwardOutlined className="ml-2" />
            </Button>
          </Link>
        </Hero>

        {/* ✅ Manter estrutura JSX idêntica */}
        <motion.div
          className="dark:bg-polar-900 flex w-full flex-col overflow-hidden rounded-2xl bg-white md:flex-row-reverse md:items-stretch"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <div className="flex flex-1 grow flex-col gap-y-10 p-8 md:p-16">
            <div className="flex flex-col gap-y-4">
              <h2 className="text-2xl leading-normal! md:text-3xl">
                Entrega Automática de Benefícios  {/* ✅ Traduzir */}
              </h2>
              <p className="dark:text-polar-500 text-lg text-gray-500">
                Configure benefícios que são entregues automaticamente quando clientes compram seus produtos. Sem trabalho manual necessário. Entrega instantânea via PIX, cartão ou boleto.  {/* ✅ Traduzir */}
              </p>
            </div>
            <motion.ul
              className="dark:divide-polar-700 dark:border-polar-700 flex flex-col divide-y divide-gray-200 border-y border-gray-200"
              variants={containerVariants}
            >
              {/* ✅ Traduzir apenas strings do array, manter estrutura do map */}
              {[
                'Chaves de licença para acesso a software',
                'Downloads automáticos de arquivos digitais',
                'Acesso automático a repositório GitHub',
                'Roles automáticos em servidor Discord',
                'Benefícios personalizados via webhooks',
              ].map((item, i) => (
                <motion.li
                  key={i}
                  className="flex items-start gap-x-3 py-2"
                  variants={itemVariants}
                >
                  <CheckOutlined
                    className="mt-0.5 text-emerald-500"
                    fontSize="small"
                  />
                  <span>{item}</span>
                </motion.li>
              ))}
            </motion.ul>
          </div>
          {/* ✅ Resto do componente permanece idêntico */}
        </motion.div>
      </Section>
    </div>
  )
}
```

---

## 🎯 Prioridade de Páginas

1. **`/features/benefits`** ⭐⭐⭐ (MAIS IMPORTANTE - Foco em entrega automática)
2. **`/features/finance`** ⭐⭐ (Métodos de pagamento brasileiros)
3. **`/features/products`** ⭐⭐ (Produtos digitais)
4. **`/features/analytics`** ⭐ (Métricas)
5. **`/features/customers`** ⭐ (Gestão de clientes)
6. **`/features/usage-billing`** ⭐ (Cobrança recorrente)

---

**Prompt criado para agente de IA front-end**  
**Versão:** 2.1.0  
**Data:** 2025-01-27  
**Foco:** Copy moderna + Automação de Entrega + PIX/Cartão/Boleto  
**⚠️ REGRA:** APENAS TEXTO - PRESERVAR 100% DA ESTRUTURA

